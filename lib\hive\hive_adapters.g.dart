// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_adapters.dart';

// **************************************************************************
// AdaptersGenerator
// **************************************************************************

class MediaLibraryAdapter extends TypeAdapter<MediaLibrary> {
  @override
  final typeId = 0;

  @override
  MediaLibrary read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MediaLibrary(
      id: (fields[0] as num).toInt(),
      name: fields[1] as String,
      url: fields[2] as String,
      mediaType: fields[3] as MediaType,
      account: fields[4] as String?,
      password: fields[5] as String?,
      isAnonymous: fields[6] == null ? false : fields[6] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, MediaLibrary obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.url)
      ..writeByte(3)
      ..write(obj.mediaType)
      ..writeByte(4)
      ..write(obj.account)
      ..writeByte(5)
      ..write(obj.password)
      ..writeByte(6)
      ..write(obj.isAnonymous);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MediaLibraryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
