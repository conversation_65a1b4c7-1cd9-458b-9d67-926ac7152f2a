import 'package:dandanplay_flutter/moudles/media_library.dart';
import 'package:signals_flutter/signals_flutter.dart';
import 'package:get_it/get_it.dart';
import 'package:dandanplay_flutter/service/storage.dart';

class MediaLibraryService {
  final Signal<List<MediaLibrary>> mediaLibraries = signal([]);

  static Future<void> register() async {
    var service = MediaLibraryService();
    await service.init();
    GetIt.I.registerSingleton<MediaLibraryService>(service);
  }

  Future<void> init() async {
    final storage = GetIt.I.get<StorageService>();
    final libraries = await storage.getMediaLibraries();
    mediaLibraries.value = libraries;
  }
}
