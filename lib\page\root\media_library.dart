import 'package:dandanplay_flutter/theme/tile_group_style.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

class MediaLibraryPage extends StatelessWidget {
  const MediaLibraryPage({super.key});

  @override
  Widget build(BuildContext context) {
    return FTileGroup(
      divider: FTileDivider.indented,
      style: tileGroupStyle(
        colors: context.theme.colors,
        typography: context.theme.typography,
        style: context.theme.style,
        newColors: context.theme.colors.copyWith(
          border: Color.fromARGB(0, 238, 238, 238),
        ),
      ),
      children: [
        FTile(
          prefixIcon: Icon(FIcons.user),
          title: const Text('Personalization'),
          onPress: () {},
        ),
        FTile(
          prefixIcon: Icon(FIcons.wifi),
          title: const Text('WiFi'),
          subtitle: const Text('Forus Labs (5G)'),
          onPress: () {},
        ),
      ],
    );
  }
}
