import 'package:dandanplay_flutter/router.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

void main() {
  runApp(const Application());
}

class Application extends StatelessWidget {
  const Application({super.key});
  @override
  Widget build(BuildContext context) => MaterialApp.router(
    builder:
        (context, child) => FTheme(data: FThemes.zinc.light, child: child!),
    routerConfig: router,
  );
}
