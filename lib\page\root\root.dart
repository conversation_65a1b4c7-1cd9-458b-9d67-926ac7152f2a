import 'package:dandanplay_flutter/page/root/home.dart';
import 'package:dandanplay_flutter/page/root/media_library.dart';
import 'package:dandanplay_flutter/page/root/my.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

final headers = [
  const FHeader(title: Text('首页')),
  FHeader(
    title: const Text('媒体库'),
    suffixes: [FHeaderAction(icon: const Icon(FIcons.plus), onPress: () {})],
  ),
  const FHeader(title: Text('个人中心')),
];

final pages = [const HomePage(), const MediaLibraryPage(), const MyPage()];

class RootPage extends StatefulWidget {
  const RootPage({super.key});

  @override
  State<RootPage> createState() => RootPageState();
}

class RootPageState extends State<RootPage> {
  int index = 1;

  @override
  Widget build(BuildContext context) => FScaffold(
    scaffoldStyle: context.theme.scaffoldStyle.copyWith(
      childPadding: EdgeInsets.all(0),
    ),
    header: Padding(padding: EdgeInsets.all(10), child: headers[index]),
    footer: FBottomNavigationBar(
      index: index,
      onChange: (index) => setState(() => this.index = index),
      children: const [
        FBottomNavigationBarItem(icon: Icon(FIcons.house), label: Text('首页')),
        FBottomNavigationBarItem(
          icon: Icon(FIcons.library),
          label: Text('媒体库'),
        ),
        FBottomNavigationBarItem(icon: Icon(FIcons.user), label: Text('个人中心')),
      ],
    ),
    child: pages[index],
  );
}
