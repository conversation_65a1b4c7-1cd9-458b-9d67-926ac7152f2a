import 'package:hive_ce/hive.dart';

enum MediaType { webdav }

class MediaLibrary extends HiveObject {
  int id;
  String name;
  String url;
  MediaType mediaType;
  String? account;
  String? password;
  bool isAnonymous;

  MediaLibrary({
    required this.id,
    required this.name,
    required this.url,
    required this.mediaType,
    this.account,
    this.password,
    this.isAnonymous = false,
  });
}
