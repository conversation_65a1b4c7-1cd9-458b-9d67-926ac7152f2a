import 'package:dandanplay_flutter/moudles/media_library.dart';
import 'package:dandanplay_flutter/hive/hive_registrar.g.dart';
import 'package:hive_ce/hive.dart';
import 'package:get_it/get_it.dart';

class StorageService {
  static late Box<MediaLibrary> mediaLibraryBox;

  static Future<void> register() async {
    final service = StorageService();
    await service.init();
    GetIt.I.registerSingleton<StorageService>(service);
  }

  Future<void> init() async {
    Hive.registerAdapters();
    mediaLibraryBox = await Hive.openBox<MediaLibrary>('mediaLibrary');
  }

  Future<List<MediaLibrary>> getMediaLibraries() async {
    return mediaLibraryBox.values.toList();
  }
}
