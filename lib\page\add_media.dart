import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import '../moudles/media_library.dart';

class AddMediaPage extends StatefulWidget {
  const AddMediaPage({super.key});

  @override
  State<AddMediaPage> createState() => _AddMediaPageState();
}

class _AddMediaPageState extends State<AddMediaPage> {
  final _formKey = GlobalKey<FormState>();
  final _mediaLibrary = MediaLibrary(
    id: 0,
    name: '',
    url: '',
    mediaType: MediaType.webdav,
    isAnonymous: false,
  );
  final _nameController = TextEditingController();
  final _urlController = TextEditingController();
  final _accountController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _urlController.dispose();
    _accountController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FTabs(
      children: [
        FTabEntry(
          label: const Text('媒体链接'),
          child: FCard(
            title: const Text('播放媒体链接'),
            subtitle: const Text('输入媒体链接播放'),
            child: Column(
              children: [
                const FTextField(label: Text('链接'), maxLength: 4),
                const SizedBox(height: 16),
                FButton(onPress: () {}, child: const Text('确定')),
              ],
            ),
          ),
        ),
        FTabEntry(
          label: const Text('媒体库'),
          child: FCard(
            title: const Text('添加媒体库'),
            subtitle: const Text('添加媒体库在首页显示'),
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  FTextField(
                    label: const Text('名称'),
                    controller: _nameController,
                  ),
                  const SizedBox(height: 10),
                  FTextField(
                    label: const Text('URL'),
                    controller: _urlController,
                  ),
                  const SizedBox(height: 10),
                  FTextField(
                    label: const Text('账号'),
                    controller: _accountController,
                  ),
                  const SizedBox(height: 10),
                  FTextField(
                    label: const Text('密码'),
                    obscureText: true,
                    controller: _passwordController,
                  ),
                  const SizedBox(height: 10),
                  FSwitch(
                    label: const Text('匿名访问'),
                    value: _mediaLibrary.isAnonymous,
                    onChange: (value) {
                      setState(() {
                        _mediaLibrary.isAnonymous = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  FButton(
                    onPress: () {
                      if (_formKey.currentState!.validate()) {
                        _mediaLibrary.name = _nameController.text;
                        _mediaLibrary.url = _urlController.text;
                        _mediaLibrary.account = _accountController.text;
                        _mediaLibrary.password = _passwordController.text;
                        // TODO: 保存媒体库
                      }
                    },
                    child: const Text('保存'),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
